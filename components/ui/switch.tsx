"use client"

import * as React from "react"
import * as SwitchPrimitives from "@radix-ui/react-switch"

import { cn } from "@/lib/utils"

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      "peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center corner-full border-2 transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50",
      // Improved contrast for unchecked state
      "data-[state=unchecked]:bg-slate-300 data-[state=unchecked]:border-slate-400 data-[state=unchecked]:hover:bg-slate-400",
      // Dark mode unchecked state
      "dark:data-[state=unchecked]:bg-slate-600 dark:data-[state=unchecked]:border-slate-500 dark:data-[state=unchecked]:hover:bg-slate-500",
      // Checked state (primary color)
      "data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:hover:bg-primary/90",
      className
    )}
    {...props}
    ref={ref}
  >
    <SwitchPrimitives.Thumb
      className={cn(
        "pointer-events-none block h-5 w-5 corner-full shadow-lg ring-0 transition-all duration-200",
        // Unchecked state thumb
        "data-[state=unchecked]:bg-white data-[state=unchecked]:border data-[state=unchecked]:border-slate-300 data-[state=unchecked]:translate-x-0",
        // Dark mode unchecked thumb
        "dark:data-[state=unchecked]:bg-slate-200 dark:data-[state=unchecked]:border-slate-400",
        // Checked state thumb
        "data-[state=checked]:bg-white data-[state=checked]:translate-x-5"
      )}
    />
  </SwitchPrimitives.Root>
))
Switch.displayName = SwitchPrimitives.Root.displayName

export { Switch }
